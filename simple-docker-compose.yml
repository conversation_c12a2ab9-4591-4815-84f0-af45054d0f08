version: '3.8'

services:
  deepfake-detector:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deepfake_detector_simple
    ports:
      - "8501:8501"
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
    networks:
      - deepfake_network

networks:
  deepfake_network:
    driver: bridge
