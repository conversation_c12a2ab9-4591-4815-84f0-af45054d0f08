# API Configuration for Deepfake Detector Backend
# From <PERSON><PERSON>'s Workspace

# Server Configuration
server:
  host: "0.0.0.0" # Server host address
  port: 8000 # Server port
  debug: false # Debug mode
  reload: false # Auto-reload on code changes
  workers: 1 # Number of worker processes

# API Settings
api:
  title: "Deepfake Video Detector API"
  description: "AI-powered deepfake video detection with explainable AI"
  version: "1.0.0"
  prefix: "/api/v1" # API prefix
  docs_url: "/docs" # Swagger documentation URL
  redoc_url: "/redoc" # ReDoc documentation URL

# File Processing
processing:
  max_file_size: 524288000 # Maximum file size (500MB)
  max_frames_per_video: 50 # Maximum frames to process per video
  default_frames_to_process: 5 # Default number of frames
  supported_formats: # Supported video formats
    - ".mp4"
    - ".avi"
    - ".mov"
    - ".mkv"
    - ".wmv"
  temp_file_cleanup: true # Auto-cleanup temporary files
  temp_file_max_age: 3600 # Max age for temp files (seconds)

# Model Configuration
model:
  model_path: "./data/models" # Path to model files
  model_name: "deepfake_detector_best.pth"
  architecture: "efficientnet_b0"
  device: "auto" # Device selection (auto, cpu, cuda)
  batch_size: 8 # Inference batch size
  confidence_threshold: 0.5 # Default confidence threshold

# Grad-CAM Configuration
gradcam:
  enabled: true # Enable Grad-CAM generation
  output_size: [224, 224] # Output image size
  colormap: "jet" # Colormap for visualization
  alpha: 0.4 # Overlay transparency
  save_visualizations: true # Save Grad-CAM images
  cleanup_old_files: true # Auto-cleanup old visualizations
  max_file_age_hours: 24 # Max age for visualization files

# Security Settings
security:
  cors_origins: # CORS allowed origins
    - "*" # Allow all origins (configure for production)
  cors_methods: # Allowed HTTP methods
    - "GET"
    - "POST"
    - "OPTIONS"
  cors_headers: # Allowed headers
    - "*"
  rate_limiting:
    enabled: true # Enable rate limiting
    requests_per_minute: 60 # Requests per minute per IP
    burst_size: 10 # Burst size for rate limiting

# Caching
caching:
  enabled: true # Enable response caching
  ttl: 3600 # Cache TTL in seconds
  max_size: 100 # Maximum cache entries
  cache_predictions: true # Cache model predictions
  cache_gradcam: true # Cache Grad-CAM results

# Logging Configuration
logging:
  level: "INFO" # Log level (DEBUG, INFO, WARNING, ERROR)
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: true # Enable file logging
  log_file: "./logs/api.log" # Log file path
  max_file_size: 10485760 # Max log file size (10MB)
  backup_count: 5 # Number of backup log files
  access_log: true # Enable access logging

# Monitoring and Health Checks
monitoring:
  health_check_enabled: true # Enable health check endpoint
  metrics_enabled: true # Enable metrics collection
  performance_monitoring: true # Monitor performance metrics
  error_tracking: true # Track and log errors

# Database Configuration (if needed)
database:
  enabled: false # Enable database logging
  url: "sqlite:///./deepfake_detector.db"
  log_requests: false # Log API requests to database
  log_predictions: false # Log predictions to database

# Background Tasks
background_tasks:
  enabled: true # Enable background task processing
  cleanup_interval: 3600 # Cleanup interval in seconds
  max_concurrent_tasks: 5 # Maximum concurrent background tasks

# Development Settings
development:
  mock_model: false # Use mock model for testing
  debug_mode: false # Enable debug features
  profiling: false # Enable performance profiling
  test_data_path: "./tests/data" # Path to test data

# Production Settings
production:
  workers: 4 # Number of worker processes
  max_requests: 1000 # Max requests per worker
  max_requests_jitter: 50 # Jitter for max requests
  preload_app: true # Preload application
  timeout: 300 # Request timeout in seconds
  keepalive: 2 # Keep-alive timeout

# Error Handling
error_handling:
  detailed_errors: false # Return detailed error messages
  log_stack_traces: true # Log full stack traces
  custom_error_pages: false # Use custom error pages

# Feature Flags
features:
  video_streaming: false # Enable video streaming support
  real_time_processing: false # Enable real-time processing
  batch_processing: true # Enable batch processing
  async_processing: true # Enable asynchronous processing
  webhook_notifications: false # Enable webhook notifications

# External Services
external_services:
  storage:
    type: "local" # Storage type (local, s3, gcs)
    path: "./data/uploads" # Local storage path

  notifications:
    enabled: false # Enable notifications
    webhook_url: "" # Webhook URL for notifications

  analytics:
    enabled: false # Enable analytics
    service: "" # Analytics service
    api_key: "" # Analytics API key
