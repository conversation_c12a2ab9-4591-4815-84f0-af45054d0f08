# Git attributes for Deepfake Video Detector
# Built from scratch by <PERSON><PERSON>'s Workspace

# Auto detect text files and perform LF normalization
* text=auto

# Python files
*.py text eol=lf
*.pyx text eol=lf
*.pyi text eol=lf

# Configuration files
*.yaml text eol=lf
*.yml text eol=lf
*.json text eol=lf
*.toml text eol=lf
*.cfg text eol=lf
*.ini text eol=lf

# Documentation
*.md text eol=lf
*.txt text eol=lf
*.rst text eol=lf

# Docker files
Dockerfile text eol=lf
*.dockerfile text eol=lf
docker-compose*.yml text eol=lf

# Shell scripts
*.sh text eol=lf
*.bash text eol=lf

# Batch files
*.bat text eol=crlf
*.cmd text eol=crlf

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pyo binary
*.pyd binary
*.so binary
*.dll binary
*.exe binary

# Model files
*.pth binary
*.pt binary
*.onnx binary
*.h5 binary
*.pkl binary
*.joblib binary

# Jupyter notebooks
*.ipynb text eol=lf

# Requirements files
requirements*.txt text eol=lf
Pipfile text eol=lf
Pipfile.lock text eol=lf

# Environment files
.env* text eol=lf

# Git files
.gitignore text eol=lf
.gitattributes text eol=lf
