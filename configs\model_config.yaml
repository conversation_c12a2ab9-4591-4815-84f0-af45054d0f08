# Model Configuration for Deepfake Detector

# Model Architecture Settings
model:
  architecture: "efficientnet_b0"  # Options: efficientnet_b0, efficientnet_b1, resnet50
  num_classes: 1                   # Binary classification (Real vs Fake)
  pretrained: true                 # Use pre-trained weights
  dropout_rate: 0.2               # Dropout rate for regularization
  
# Training Configuration
training:
  batch_size: 32                  # Training batch size
  learning_rate: 0.0001           # Initial learning rate
  num_epochs: 50                  # Maximum number of epochs
  optimizer: "adam"               # Optimizer type (adam, sgd, adamw)
  weight_decay: 0.0001           # L2 regularization
  scheduler: "cosine"             # Learning rate scheduler
  early_stopping_patience: 10    # Early stopping patience
  
# Data Configuration
data:
  input_size: [224, 224]          # Input image size [height, width]
  normalize_mean: [0.485, 0.456, 0.406]  # ImageNet normalization mean
  normalize_std: [0.229, 0.224, 0.225]   # ImageNet normalization std
  frame_rate: 5                   # Frames per second to extract
  max_frames_per_video: 50        # Maximum frames per video
  train_split: 0.7               # Training data split ratio
  val_split: 0.2                 # Validation data split ratio
  test_split: 0.1                # Test data split ratio
  
# Data Augmentation
augmentation:
  enabled: true
  horizontal_flip: 0.5           # Probability of horizontal flip
  rotation_range: 10             # Rotation range in degrees
  brightness_range: [0.8, 1.2]  # Brightness adjustment range
  contrast_range: [0.8, 1.2]    # Contrast adjustment range
  saturation_range: [0.8, 1.2]  # Saturation adjustment range
  hue_range: [-0.1, 0.1]        # Hue adjustment range
  gaussian_blur: 0.1             # Probability of Gaussian blur
  noise_std: 0.01               # Gaussian noise standard deviation
  
# Loss Function
loss:
  type: "bce_with_logits"        # Binary cross-entropy with logits
  pos_weight: 1.0               # Positive class weight for imbalanced data
  label_smoothing: 0.0          # Label smoothing factor
  
# Evaluation Metrics
metrics:
  - "accuracy"
  - "precision"
  - "recall"
  - "f1_score"
  - "auc_roc"
  - "confusion_matrix"
  
# Model Checkpointing
checkpointing:
  save_best_only: true          # Save only the best model
  monitor: "val_accuracy"       # Metric to monitor for best model
  mode: "max"                   # Maximize or minimize the monitored metric
  save_frequency: 5             # Save checkpoint every N epochs
  
# Logging and Monitoring
logging:
  log_level: "INFO"
  log_frequency: 100            # Log every N batches
  tensorboard_enabled: true     # Enable TensorBoard logging
  wandb_enabled: false          # Enable Weights & Biases logging
  
# Hardware Configuration
hardware:
  device: "auto"                # Device to use (auto, cpu, cuda)
  mixed_precision: true         # Use mixed precision training
  num_workers: 4                # Number of data loader workers
  pin_memory: true              # Pin memory for faster data transfer
  
# Explainability
explainability:
  gradcam_enabled: true         # Enable Grad-CAM generation
  gradcam_layer: "auto"         # Target layer for Grad-CAM (auto-detect)
  save_gradcam_samples: 10      # Number of Grad-CAM samples to save
  
# Model Optimization
optimization:
  quantization_enabled: false   # Enable model quantization
  pruning_enabled: false        # Enable model pruning
  knowledge_distillation: false # Enable knowledge distillation
  
# Ensemble Configuration
ensemble:
  enabled: false                # Enable ensemble training
  num_models: 3                 # Number of models in ensemble
  architectures:                # Different architectures for ensemble
    - "efficientnet_b0"
    - "efficientnet_b1"
    - "resnet50"
