version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: deepfake_detector_backend
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - MODEL_PATH=/app/data/models
      - UPLOAD_DIR=/app/data/uploads
      - OUTPUT_DIR=/app/data/outputs
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
    networks:
      - deepfake_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: deepfake_detector_frontend
    ports:
      - "8501:8501"
    environment:
      - BACKEND_URL=http://backend:8000
      - PYTHONPATH=/app
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - deepfake_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  deepfake_network:
    driver: bridge

volumes:
  model_data:
  upload_data:
  output_data:
