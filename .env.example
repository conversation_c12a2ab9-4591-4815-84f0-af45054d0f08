# Environment Variables Example
# Copy this file to .env and update with your values

# Application Environment
ENVIRONMENT=development
DEBUG=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
BACKEND_URL=http://localhost:8000

# Paths
MODEL_PATH=./data/models
UPLOAD_DIR=./data/uploads
OUTPUT_DIR=./data/outputs
TEMP_DIR=./data/temp
LOG_DIR=./logs

# Model Settings
MODEL_NAME=deepfake_detector_best.pth
MODEL_ARCHITECTURE=efficientnet_b0
CONFIDENCE_THRESHOLD=0.5

# Processing Settings
MAX_FILE_SIZE=524288000
MAX_FRAMES_PER_VIDEO=50
DEFAULT_FRAMES_TO_PROCESS=5

# Security
CORS_ORIGINS=*
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Features
GRADCAM_ENABLED=true
CACHING_ENABLED=true
AUTO_CLEANUP=true

# Hardware
DEVICE=auto
BATCH_SIZE=8
NUM_WORKERS=4

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# External Services (Optional)
# WEBHOOK_URL=
# ANALYTICS_API_KEY=
# STORAGE_TYPE=local
